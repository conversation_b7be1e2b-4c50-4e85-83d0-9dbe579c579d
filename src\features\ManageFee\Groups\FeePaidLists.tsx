/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, Component } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import BasicFeeSetting from '@/features/ManageFee/BasicFeeSetting/BasicFeeSetting';
import FeeDateSettings from '@/features/ManageFee/FeeDateSetting/FeeDateSettings';
import Scholarship from '@/features/ManageFee/Scholarship/Scholarship';
import OptionalFeeSetting from '@/features/ManageFee/OptionalFee/OptionalFeeSetting';
import Fine from '@/features/ManageFee/Fine/Fine';
import IndividualFeeSetting from '@/features/ManageFee/IndividualFeeSetting';
import OptionalFeeSetting2 from '@/features/ManageFee/OptionalFeeSetting2';

const FeeSettingsGroupRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding: 1rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[50] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

// type FeeType = 'amount' | 'date' | 'scholarship' | 'fine' | 'optional' | 'individual' | 'busstop';
const options = [
  { path: '/manage-fee/setting', Component: <BasicFeeSetting /> },
  { path: '/manage-fee/fee-date-settings', Component: <FeeDateSettings /> },
  { path: '/manage-fee/scholarship', Component: <Scholarship /> },
  { path: '/manage-fee/fine-setting', Component: <Fine /> },
  { path: '/manage-fee/optional-fee-settings', Component: <OptionalFeeSetting /> },
  { path: '/manage-fee/individual-fee-settings', Component: <IndividualFeeSetting /> },
  { path: '/manage-fee/bus-stop-settings', Component: <OptionalFeeSetting2 /> },
];
function FeeSettingsGroup() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState(0);

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    const index = options.findIndex((option) => option.path === currentPath);
    if (index !== -1) {
      setFeeType(index);
    }
  }, [location.pathname]);

  const handleFeeTypeChange = (event: React.MouseEvent<HTMLElement>, newFeeType: number | null) => {
    if (newFeeType !== null) {
      setFeeType(newFeeType);
      // Update URL based on selection
      if (newFeeType === 0) {
        navigate('/manage-fee/setting', { replace: true });
      } else if (newFeeType === 1) {
        navigate('/manage-fee/fee-date-settings', { replace: true });
      } else if (newFeeType === 2) {
        navigate('/manage-fee/scholarship', { replace: true });
      } else if (newFeeType === 3) {
        navigate('/manage-fee/fine-setting', { replace: true });
      } else if (newFeeType === 4) {
        navigate('/manage-fee/optional-fee-settings', { replace: true });
      } else if (newFeeType === 5) {
        navigate('/manage-fee/individual-fee-settings', { replace: true });
      } else if (newFeeType === 6) {
        navigate('/manage-fee/bus-stop-settings', { replace: true });
      }
    }
  };
  return (
    <Page title="Fee List Groups">
      <FeeSettingsGroupRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
            <Typography
              variant="h6"
              fontSize={20}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Settings
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="medium"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              <ToggleButton value={0}>Fee Amount</ToggleButton>
              <ToggleButton value={1}>Fee Date</ToggleButton>
              <ToggleButton value={2}>Scholarship</ToggleButton>
              <ToggleButton value={3}>Fine</ToggleButton>
              <ToggleButton value={4}>Optional Fee</ToggleButton>
              <ToggleButton value={5}>Individual Fee</ToggleButton>
              <ToggleButton value={6}>Bus Stop</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">{options[feeType].Component}</div>
      </FeeSettingsGroupRoot>
    </Page>
  );
}

export default FeeSettingsGroup;
