/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import BasicFeeList from '@/features/ManageFee/FeesList/BasicFeeList';
import TermFeeList from '@/features/ManageFee/TermFeeList/TermFeeList';

const FeeListGroupRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding: 1rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[50] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

type FeeType = 'basic' | 'term';

function FeeListGroup() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState<FeeType>('basic');

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath.includes('term-fee')) {
      setFeeType('term');
    } else {
      setFeeType('basic');
    }
  }, [location.pathname]);

  const handleFeeTypeChange = (event: React.MouseEvent<HTMLElement>, newFeeType: FeeType | null) => {
    if (newFeeType !== null) {
      setFeeType(newFeeType);
      // Update URL based on selection
      if (newFeeType === 'basic') {
        navigate('/manage-fee/basic-fee-list', { replace: true });
      } else {
        navigate('/manage-fee/term-fee-list', { replace: true });
      }
    }
  };
  return (
    <Page title="Fee List Groups">
      <FeeListGroupRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
            <Typography
              variant="h6"
              fontSize={20}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Management
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="medium"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              <ToggleButton value="basic">Basic Fee</ToggleButton>
              <ToggleButton value="term">Term Fee</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">{feeType === 'basic' ? <BasicFeeList /> : <TermFeeList />}</div>
      </FeeListGroupRoot>
    </Page>
  );
}

export default FeeListGroup;
