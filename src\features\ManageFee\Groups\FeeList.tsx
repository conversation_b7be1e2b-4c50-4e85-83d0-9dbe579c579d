/* eslint-disable no-nested-ternary */
import React, { useState } from 'react';
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Autocomplete,
  Box,
  ToggleButtonGroup,
  ToggleButton,
  Divider,
  Grid,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  useTheme,
} from '@mui/material';
import styled from 'styled-components';
import { CLASS_SELECT, STATUS_SELECT, YEAR_SELECT } from '@/config/Selection';
import SearchIcon from '@mui/icons-material/Search';
import Popup from '@/components/shared/Popup/Popup';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';

import { MdAdd } from 'react-icons/md';
import CreateFeeList from './CreateFeeList';
import MapFeeList from './MapFeeList';
import DescExamList from '../DescriptiveExam/DescExamList/DescExamList';
import ObjExamList from '../ObjectiveExam/ObjExamList/ObjExamList';
import ViewObjExam from '../ObjectiveExam/ObjExamList/ViewObjExam';


const FeeListListRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  padding: 1rem;

  .student_card {
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  }

  .Card {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 996px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 40px);
      flex-grow: 1;

      .card-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        ::-webkit-scrollbar {
          width: 0px;
        }

        .MuiTableContainer-root {
          max-height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
      }
    }
  }
`;

function FeeListList() {
  const theme = useTheme();
  // const { themeMode } = useSettings();
  // const isLight = themeMode === 'light';
  // const [changeView, setChangeView] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  // const [view, setView] = useState(false);
  const [view, setView] = useState<'ObjExamList' | 'DescExamList' | 'ViewObjExam' | 'DescObjExam'>('ObjExamList');
  const [Delete, setDelete] = useState(false);
  const [createPopup, setCreatePopup] = useState(false);
  const [mapPopup, setMapPopup] = useState(false);

  const handleClickCloseDelete = () => setDelete(false);

  const [examType, setExamType] = React.useState('obj');

  const handleClickView = () => setView('ViewObjExam');
  const handleClickCloseView = () => setView('ObjExamList');

  const handleChange = (event: React.MouseEvent<HTMLElement>, newExamType: string) => {
    setExamType(newExamType);
  };
  //   const renderContent = () => {
  //     return students.map((student, rowIndex) => (
  //       <Card key={student.id}>
  //         {FeeListListColumns.map((column) => (
  //           <Typography key={column.name}>{student[column.dataKey]}</Typography>
  //         ))}
  //       </Card>
  //     ));
  //   };

  // const content: ReactNode = data.map((item, rowIndex: number) => {
  //   let cellData: ReactNode = null;

  //   return (
  //     <Typography key={column.name} variant="subtitle1" mb={1} fontSize={13}>
  //       <b>{cellData}</b>
  //     </Typography>
  //   );
  // });

  // Content = content;

  return view === 'ViewObjExam' ? (
    <ViewObjExam onBackClick={handleClickCloseView} />
  ) : (
    <Page title="Schedule List">
      <FeeListListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={20}>
              Manage Exams
            </Typography>
            <ToggleButtonGroup value={examType} exclusive onChange={handleChange} aria-label="Platform" size="small">
              <ToggleButton value="obj" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Objective(MCQ) Exams
              </ToggleButton>
              <ToggleButton value="desc" sx={{ fontWeight: 900, color: theme.palette.grey[500] }}>
                Descriptive Exams
              </ToggleButton>
            </ToggleButtonGroup>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              <Tooltip title="Search">
                <IconButton aria-label="" color="primary" sx={{ mr: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <SearchIcon />
                </IconButton>
              </Tooltip>
              <Button
                sx={{ borderRadius: '20px' }}
                size="small"
                variant="outlined"
                onClick={() => setCreatePopup(true)}
              >
                <MdAdd size="20px" /> Create
              </Button>
              <Button
                sx={{ borderRadius: '20px', ml: 1 }}
                size="small"
                variant="contained"
                onClick={() => setMapPopup(true)}
              >
                Map Last Year Exams
              </Button>
            </Box>
          </Stack>
          <Stack>
            <Divider />
          </Stack>
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate>
                <Grid pb={2} pt={1} container spacing={2}>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Autocomplete
                        options={YEAR_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select year" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', sm: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Autocomplete
                        options={CLASS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select class" />}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto" md={6} xs={12}>
                    <FormControl sx={{ minWidth: { xs: '100%', lg: 150 } }}>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Status
                      </Typography>
                      <Autocomplete
                        options={STATUS_SELECT}
                        renderInput={(params) => <TextField {...params} placeholder="Select" />}
                      />
                    </FormControl>
                  </Grid>

                  <Grid item lg={1} xs={12}>
                    <Stack spacing={2} direction="row" sx={{ pt: { xs: 0, md: 3.79 } }}>
                      <Button variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                      {/* <Button variant="contained" color="primary" fullWidth>
                          Search
                        </Button> */}
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            {examType === 'obj' ? <ObjExamList handleClickView={handleClickView} /> : <DescExamList />}
          </div>
        </Card>
      </FeeListListRoot>
      <Popup
        size="xs"
        state={Delete}
        onClose={handleClickCloseDelete}
        popupContent={<DeleteMessage message="Are you sure want to delete?" />}
      />
      {/* <Popup size="lg" state={view} popupContent={<ViewFeeList onClose={() => handleClickCloseView()} />} /> */}
      <Popup
        size="md"
        title="Create New Online Exam"
        state={createPopup}
        onClose={() => setCreatePopup(false)}
        popupContent={<CreateFeeList />}
      />
      <Popup
        size="md"
        title="Map Online Exam"
        state={mapPopup}
        onClose={() => setMapPopup(false)}
        popupContent={<MapFeeList />}
      />
    </Page>
  );
}

export default FeeListList;
